# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "pip" # See documentation for possible values
    directory: "/" # Location of package manifests
    target-branch: "dev"
    schedule:
      interval: "daily"
    ignore:
      - dependency-name: "pytorch-lightning"
        update-types: ["version-update:semver-patch"]
      - dependency-name: "torchmetrics"
        update-types: ["version-update:semver-patch"]
